export class Piece {
    constructor(color, type) {
        this.color = color; // 'white' hoặc 'black'
        this.type = type;   // 'king', 'queen', ...
        this.hasMoved = false; // Đ<PERSON> kiểm tra nước đi đầu tiên của quân
    }

    getPossibleMoves(board, x, y) {
        switch(this.type) {
            case 'pawn':
                return this.getPawnMoves(board, x, y);
            case 'rook':
                return this.getRookMoves(board, x, y);
            case 'knight':
                return this.getKnightMoves(board, x, y);
            case 'bishop':
                return this.getBishopMoves(board, x, y);
            case 'queen':
                return this.getQueenMoves(board, x, y);
            case 'king':
                return this.getKingMoves(board, x, y);
            default:
                return [];
        }
    }

    getPawnMoves(board, x, y) {
        const moves = [];
        const direction = this.color === 'white' ? -1 : 1;
        
        // Nước đi thẳng
        if (!board.grid[y + direction][x]) {
            moves.push({ x, y: y + direction });
            
            // Nước đi 2 ô đầu tiên
            if (!this.hasMoved && !board.grid[y + 2 * direction][x]) {
                moves.push({ x, y: y + 2 * direction });
            }
        }
        
        // Nước ăn chéo
        const captureDirections = [-1, 1];
        captureDirections.forEach(dx => {
            const newX = x + dx;
            const newY = y + direction;
            
            if (newX >= 0 && newX < 8 && newY >= 0 && newY < 8) {
                const targetPiece = board.grid[newY][newX];
                if (targetPiece && targetPiece.color !== this.color) {
                    moves.push({ x: newX, y: newY });
                }
            }
        });
        
        return moves;
    }

    getRookMoves(board, x, y) {
        const moves = [];
        const directions = [
            {dx: 1, dy: 0}, {dx: -1, dy: 0},
            {dx: 0, dy: 1}, {dx: 0, dy: -1}
        ];
        
        directions.forEach(dir => {
            for (let i = 1; i < 8; i++) {
                const newX = x + dir.dx * i;
                const newY = y + dir.dy * i;
                
                if (newX < 0 || newX >= 8 || newY < 0 || newY >= 8) break;
                
                const targetPiece = board.grid[newY][newX];
                if (!targetPiece) {
                    moves.push({ x: newX, y: newY });
                } else {
                    if (targetPiece.color !== this.color) {
                        moves.push({ x: newX, y: newY });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }

    getKnightMoves(board, x, y) {
        const moves = [];
        const knightMoves = [
            {dx: 2, dy: 1}, {dx: 2, dy: -1},
            {dx: -2, dy: 1}, {dx: -2, dy: -1},
            {dx: 1, dy: 2}, {dx: 1, dy: -2},
            {dx: -1, dy: 2}, {dx: -1, dy: -2}
        ];
        
        knightMoves.forEach(move => {
            const newX = x + move.dx;
            const newY = y + move.dy;
            
            if (newX >= 0 && newX < 8 && newY >= 0 && newY < 8) {
                const targetPiece = board.grid[newY][newX];
                if (!targetPiece || targetPiece.color !== this.color) {
                    moves.push({ x: newX, y: newY });
                }
            }
        });
        
        return moves;
    }

    getBishopMoves(board, x, y) {
        const moves = [];
        const directions = [
            {dx: 1, dy: 1}, {dx: 1, dy: -1},
            {dx: -1, dy: 1}, {dx: -1, dy: -1}
        ];
        
        directions.forEach(dir => {
            for (let i = 1; i < 8; i++) {
                const newX = x + dir.dx * i;
                const newY = y + dir.dy * i;
                
                if (newX < 0 || newX >= 8 || newY < 0 || newY >= 8) break;
                
                const targetPiece = board.grid[newY][newX];
                if (!targetPiece) {
                    moves.push({ x: newX, y: newY });
                } else {
                    if (targetPiece.color !== this.color) {
                        moves.push({ x: newX, y: newY });
                    }
                    break;
                }
            }
        });
        
        return moves;
    }

    getQueenMoves(board, x, y) {
        return [
            ...this.getRookMoves(board, x, y),
            ...this.getBishopMoves(board, x, y)
        ];
    }

    getKingMoves(board, x, y) {
        const moves = [];
        const kingMoves = [
            {dx: 1, dy: 0}, {dx: -1, dy: 0},
            {dx: 0, dy: 1}, {dx: 0, dy: -1},
            {dx: 1, dy: 1}, {dx: 1, dy: -1},
            {dx: -1, dy: 1}, {dx: -1, dy: -1}
        ];
        
        kingMoves.forEach(move => {
            const newX = x + move.dx;
            const newY = y + move.dy;
            
            if (newX >= 0 && newX < 8 && newY >= 0 && newY < 8) {
                const targetPiece = board.grid[newY][newX];
                if (!targetPiece || targetPiece.color !== this.color) {
                    moves.push({ x: newX, y: newY });
                }
            }
        });
        
        return moves;
    }
} 