require('dotenv').config();
const app = require('./app');

const PORT = process.env.PORT || 3001;

const { connectDB } = require('./src/configs/database.config');

let server;

connectDB().then(() => {
    server = app.listen(PORT, () => {
        console.log(`Server is running on port ${PORT}`);
    });
});

process.on('SIGINT', () => {
    server.close(() => console.log('Exit Server Express'));
});
