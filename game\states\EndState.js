export class EndState {
    constructor(game, winner) {
        this.game = game;
        this.winner = winner;
    }

    update(delta) {
        // Xử lý logic kết thúc
    }

    render(renderer) {
        renderer.ctx.clearRect(0, 0, renderer.size, renderer.size);
        renderer.ctx.fillStyle = '#222';
        renderer.ctx.fillRect(0, 0, renderer.size, renderer.size);
        renderer.ctx.fillStyle = '#fff';
        renderer.ctx.font = '36px Arial';
        renderer.ctx.textAlign = 'center';
        renderer.ctx.fillText('Kết thúc game!', renderer.size / 2, renderer.size / 2 - 20);
        renderer.ctx.font = '24px Arial';
        renderer.ctx.fillText(`Người thắng: ${this.winner}`, renderer.size / 2, renderer.size / 2 + 30);
    }
} 