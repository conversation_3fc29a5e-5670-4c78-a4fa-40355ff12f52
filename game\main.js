import { Game } from './core/Game.js';
import { CanvasRenderer } from './ui/CanvasRenderer.js';
import { MenuState } from './states/MenuState.js';
import { PlayState } from './states/PlayState.js';
import { EndState } from './states/EndState.js';

const canvas = document.getElementById('game-canvas');
const renderer = new CanvasRenderer(canvas);
const game = new Game(renderer);

game.setState(new MenuState(game));

game.start();