export class CanvasRenderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.size = canvas.width;
        this.squareSize = this.size / 8;
        this.selectedPiece = null;
    }

    drawBoard(grid, selectedSquare = null, possibleMoves = [], playerColor = 'white') {
        // <PERSON><PERSON><PERSON> sạch canvas
        this.ctx.clearRect(0, 0, this.size, this.size);

        // Vẽ nền
        this.drawBackground();

        // Lưu trạng thái vẽ
        this.ctx.save();

        // Xoay bàn cờ nếu người chơi chọn màu đen
        if (playerColor === 'black') {
            this.ctx.translate(this.size / 2, this.size / 2);
            this.ctx.rotate(Math.PI);
            this.ctx.translate(-this.size / 2, -this.size / 2);
        }

        // Vẽ bàn cờ
        for (let y = 0; y < 8; y++) {
            for (let x = 0; x < 8; x++) {
                // Vẽ ô cờ với hiệu ứng 3D
                this.drawSquare(x, y);
                
                // Vẽ quân cờ
                const piece = grid[y][x];
                if (piece) {
                    this.drawPiece(piece, x, y);
                }
            }
        }

        // Vẽ border cho ô được chọn
        if (selectedSquare) {
            this.drawSquareBorder(selectedSquare.x, selectedSquare.y, 'rgba(255, 255, 0, 0.7)', 5);
        }

        // Vẽ border cho các nước đi hợp lệ
        possibleMoves.forEach(move => {
            this.drawSquareBorder(move.x, move.y, 'rgba(0, 255, 0, 0.7)', 3);
        });

        // Khôi phục trạng thái vẽ
        this.ctx.restore();
    }

    drawBackground() {
        // Gradient nền
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.size);
        gradient.addColorStop(0, '#4a4a4a');
        gradient.addColorStop(1, '#2c2c2c');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.size, this.size);
    }

    drawSquare(x, y) {
        // Màu ô cờ
        const lightSquare = '#f0d9b5';
        const darkSquare = '#b58863';
        
        // Vẽ đổ bóng
        this.ctx.shadowColor = 'rgba(0,0,0,0.3)';
        this.ctx.shadowBlur = 5;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        // Chọn màu
        this.ctx.fillStyle = (x + y) % 2 === 0 ? lightSquare : darkSquare;
        
        // Vẽ ô
        this.ctx.fillRect(
            x * this.squareSize, 
            y * this.squareSize, 
            this.squareSize, 
            this.squareSize
        );

        // Tắt đổ bóng
        this.ctx.shadowBlur = 0;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;
    }

    drawPiece(piece, x, y) {
        // Chuẩn bị vẽ quân cờ
        this.ctx.save();

        // Đổ bóng cho quân cờ
        this.ctx.shadowColor = 'rgba(0,0,0,0.5)';
        this.ctx.shadowBlur = 5;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        // Vẽ quân cờ với hiệu ứng 3D
        const centerX = (x + 0.5) * this.squareSize;
        const centerY = (y + 0.5) * this.squareSize;

        // Vẽ biểu tượng quân cờ
        const pieceSymbols = {
            'king': '♔', 'queen': '♕', 'rook': '♖', 
            'bishop': '♗', 'knight': '♘', 'pawn': '♙'
        };

        const blackPieceSymbols = {
            'king': '♚', 'queen': '♛', 'rook': '♜', 
            'bishop': '♝', 'knight': '♞', 'pawn': '♟'
        };

        this.ctx.fillStyle = piece.color === 'white' ? '#000' : '#fff';
        this.ctx.font = `${this.squareSize * 0.6}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        const symbol = piece.color === 'white' 
            ? pieceSymbols[piece.type] 
            : blackPieceSymbols[piece.type];
        
        this.ctx.fillText(symbol, centerX, centerY);

        // Khôi phục trạng thái vẽ
        this.ctx.restore();
    }

    drawSquareBorder(x, y, color, lineWidth) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = lineWidth;
        this.ctx.strokeRect(
            x * this.squareSize + lineWidth/2, 
            y * this.squareSize + lineWidth/2, 
            this.squareSize - lineWidth, 
            this.squareSize - lineWidth
        );
    }
} 