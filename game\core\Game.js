export class Game {
    constructor(renderer) {
        this.renderer = renderer;
        this.currentState = null;
        this.lastTimestamp = 0;
    }

    setState(state) {
        this.currentState = state;
    }

    start() {
        requestAnimationFrame(this.loop.bind(this));
    }

    loop(timestamp) {
        const delta = timestamp - this.lastTimestamp;
        this.lastTimestamp = timestamp;
        if (this.currentState) {
            this.currentState.update(delta);
            this.currentState.render(this.renderer);
        }
        requestAnimationFrame(this.loop.bind(this));
    }
} 